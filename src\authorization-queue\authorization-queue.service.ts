import { Injectable, NotFoundException } from '@nestjs/common';
import { AllowanceService } from 'src/allowance/allowance.service';
import { AuthTokenService } from 'src/auth-token/auth-token.service';
import { BranchService } from 'src/branch/branch.service';
import { ContractTypeService } from 'src/contract-type/contract-type.service';
import { DatabaseService } from 'src/database/database.service';
import { DeductionService } from 'src/deduction/deduction.service';
import { DepartmentService } from 'src/department/department.service';
import { DesignationService } from 'src/designation/designation.service';
import { EmployeeGroupService } from 'src/employee-group/employee-group.service';
import { EmployeeService } from 'src/employee/employee.service';
import { GradeService } from 'src/grade/grade.service';
import { PayrollService } from 'src/payroll/payroll.service';
import { RegionService } from 'src/region/region.service';
import { RoleService } from 'src/role/role.service';
import { SalaryPackageService } from 'src/salary-package/salary-package.service';
import { SubBranchService } from 'src/sub-branch/sub-branch.service';
import { TaxJurisdictionService } from 'src/tax-jurisdiction/tax-jurisdiction.service';
import { UnitService } from 'src/unit/unit.service';
import { UsersService } from 'src/users/users.service';
import { ACTIONS_CONSTANT } from './constants/entity.constants';
import { MODULE_CONSTANT } from './constants/module.constant';

@Injectable()
export class AuthorizationService {
  constructor(
    private readonly databaseService: DatabaseService,
    private readonly authTokenService: AuthTokenService,
    private readonly roleService: RoleService,
    private readonly employeeGroupService: EmployeeGroupService,
    private readonly branchService: BranchService,
    private readonly employeeService: EmployeeService,
    private readonly gradeService: GradeService,
    private readonly departmentService: DepartmentService,
    private readonly unitService: UnitService,
    private readonly contractTypeService: ContractTypeService,
    private readonly designationService: DesignationService,
    private readonly salaryPackageService: SalaryPackageService,
    private readonly allowanceService: AllowanceService,
    private readonly deductionService: DeductionService,
    private readonly payrollService: PayrollService,
    private readonly userService: UsersService,
    private readonly taxJurisdictionService: TaxJurisdictionService,
    private readonly regionService: RegionService,
    private readonly subBranchService: SubBranchService,
  ) {}

  async createRequest({
    payload,
    token,
    action,
    module,
  }: {
    token: string;
    payload: any;
    action: ACTIONS_CONSTANT;
    module: MODULE_CONSTANT;
  }) {
    const decodedToken = await this.authTokenService.decodeToken(token);

    try {
      await this.databaseService.authorizationQueue.create({
        data: {
          data: JSON.stringify(payload),
          action: action,
          companyId: decodedToken.companyId,
          module: module,
          requestedBy: decodedToken.name || decodedToken.email,
        },
      });
      return { message: 'Request successful and pending authoriztion' };
    } catch (error) {
      console.log(error);

      throw new Error('Something went wrong. Try again!');
    }
  }

  async getAuthorizationQueue(token: string) {
    try {
      const decodedToken = await this.authTokenService.decodeToken(token);
      const queue = await this.databaseService.authorizationQueue.findMany({
        where: {
          companyId: decodedToken.companyId,
        },
        orderBy: {
          createdAt: 'desc',
        },
      });

      return queue;
    } catch (error) {
      console.log(error);

      throw new Error('Failed to fetch entities');
    }
  }
  async acceptRequest({ queueId, token }: { queueId: string; token: string }) {
    try {
      let success: boolean;
      const decodedToken = await this.authTokenService.decodeToken(token);

      const approvedBy = decodedToken.name || decodedToken.email;
      const queue = await this.databaseService.authorizationQueue.findUnique({
        where: {
          id: queueId,
        },
      });

      if (!queue) {
        throw new NotFoundException('Request not found');
      }

      switch (queue?.module as MODULE_CONSTANT) {
        case MODULE_CONSTANT.ROLE:
          {
            success = await this.roleService.acceptRoleAction({
              approvedBy,
              queue,
            });
          }
          break;
        case MODULE_CONSTANT.EMPLOYEE_GROUP:
          {
            success = await this.employeeGroupService.acceptEmployeeGroupAction(
              {
                approvedBy,
                queue,
              },
            );
          }
          break;
        case MODULE_CONSTANT.GRADE:
          {
            success = await this.gradeService.acceptGradeAction({
              approvedBy,
              queue,
            });
          }
          break;
        case MODULE_CONSTANT.BRANCH:
          {
            success = await this.branchService.acceptBranchAction({
              approvedBy,
              queue,
            });
          }
          break;
        case MODULE_CONSTANT.DEPARTMENT:
          {
            success = await this.departmentService.acceptDepartmentAction({
              approvedBy,
              queue,
            });
          }
          break;
        case MODULE_CONSTANT.UNIT:
          {
            success = await this.unitService.acceptUnitAction({
              approvedBy,
              queue,
            });
          }
          break;
        case MODULE_CONSTANT.CONTRACT_TYPE:
          {
            success = await this.contractTypeService.acceptContractTypeAction({
              approvedBy,
              queue,
            });
          }
          break;
        case MODULE_CONSTANT.DESIGNATION:
          {
            success = await this.designationService.acceptDesignationAction({
              approvedBy,
              queue,
            });
          }
          break;
        case MODULE_CONSTANT.EMPLOYEE:
          {
            success = await this.employeeService.acceptEmployeeAction({
              approvedBy,
              queue,
            });
          }
          break;
        case MODULE_CONSTANT.SALARY_PACKAGE:
          {
            success = await this.salaryPackageService.acceptSalaryPackageAction(
              {
                approvedBy,
                queue,
              },
            );
          }
          break;
        case MODULE_CONSTANT.ALLOWANCE:
          {
            success = await this.allowanceService.acceptAllowanceAction({
              approvedBy,
              queue,
            });
          }
          break;
        case MODULE_CONSTANT.DEDUCTION:
          {
            success = await this.deductionService.acceptDeductionAction({
              approvedBy,
              queue,
            });
          }
          break;
        case MODULE_CONSTANT.PAYROLL:
          {
            success = await this.payrollService.acceptPayrollAction({
              approvedBy,
              queue,
            });
          }
          break;
        case MODULE_CONSTANT.USER: {
          success = await this.userService.acceptUserAction({
            approvedBy,
            queue,
          });
          break;
        }
        case MODULE_CONSTANT.TAX_JURISDICTION: {
          success =
            await this.taxJurisdictionService.acceptTaxJurisdictionAction({
              approvedBy,
              queue,
            });
          break;
        }
        case MODULE_CONSTANT.REGION: {
          success = await this.regionService.acceptRegionAction({
            approvedBy,
            queue,
          });
          break;
        }
        case MODULE_CONSTANT.SUB_BRANCH: {
          success = await this.subBranchService.acceptSubBranchAction({
            approvedBy,
            queue,
          });
          break;
        }
        default:
          success = false;
          break;
      }

      if (success) {
        await this.databaseService.authorizationQueue.update({
          where: {
            id: queue.id,
          },
          data: {
            status: 'APPROVED',
          },
        });
      }

      return queue;
    } catch (error) {
      console.log(error);

      throw error;
    }
  }

  async rejectRequest({ queueId, token }: { queueId: string; token: string }) {
    try {
      await this.authTokenService.decodeToken(token);

      const queue = await this.databaseService.authorizationQueue.findUnique({
        where: {
          id: queueId,
        },
      });

      if (!queue) {
        throw new NotFoundException('Request not found');
      }

      await this.databaseService.authorizationQueue.update({
        where: {
          id: queue.id,
        },
        data: {
          status: 'REJECTED',
        },
      });

      return queue;
    } catch (error) {
      console.log(error);

      throw error;
    }
  }
}
